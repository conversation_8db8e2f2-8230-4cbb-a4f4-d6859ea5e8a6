#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import axios from "axios";
import * as cheerio from "cheerio";

// Axmol 相关 URL 配置
const AXMOL_REPO = "axmolengine/axmol";
const AXMOL_WIKI_BASE = "https://github.com/axmolengine/axmol/wiki";
const GITHUB_API_BASE = "https://api.github.com";
const GITHUB_RAW_BASE = "https://raw.githubusercontent.com";

// GitHub API 配置
const GITHUB_HEADERS = {
  'Accept': 'application/vnd.github.v3+json',
  'User-Agent': 'Axmol-MCP-Assistant',
  'X-GitHub-Api-Version': '2022-11-28'
};

// Wiki 页面请求头
const WIKI_HEADERS = {
  'User-Agent': 'Axmol-MCP-Assistant',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'en-US,en;q=0.5',
  'Accept-Encoding': 'gzip, deflate',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1'
};

/**
 * Axmol MCP 服务器主入口
 */
async function main() {
  // 启动日志

  const server = new Server({
    name: "axmol-mcp-server",
    version: "1.0.0",
  });

  // 注册统一的智能问答工具
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    return {
      tools: [
        {
          name: "axmol_smart_assistant",
          description:
            "Axmol 实时智能助手 - 实时获取 Axmol 引擎最新源码、文档和示例，提供最新最全的开发解决方案",
          inputSchema: {
            type: "object",
            properties: {
              question: {
                type: "string",
                description: "您想要询问的 Axmol 开发问题（支持中文和英文）",
              },
              context: {
                type: "string",
                description:
                  "可选的上下文信息，比如您正在开发的功能类型、目标平台等",
                default: "",
              },
              includeCode: {
                type: "boolean",
                description: "是否需要包含代码示例",
                default: true,
              },
              language: {
                type: "string",
                enum: ["cpp", "lua", "both"],
                description: "代码示例语言偏好",
                default: "cpp",
              },
            },
            required: ["question"],
          },
        },
      ],
    };
  });

  // 注册工具调用处理器
  server.setRequestHandler(CallToolRequestSchema, async request => {
    // 处理工具调用请求
    const { name, arguments: args } = request.params;

    try {
      switch (name) {
        case "axmol_smart_assistant":
          return await handleSmartAssistant(args as any);
        default:
          throw new Error(`未知工具: ${name}`);
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `错误: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  });

  // 启动服务器
  const transport = new StdioServerTransport();
  await server.connect(transport);
  // 服务器启动完成
}

/**
 * 智能助手主处理函数
 */
async function handleSmartAssistant(args: {
  question: string;
  context?: string;
  includeCode?: boolean;
  language?: string;
}): Promise<any> {
  const { question, context = "", includeCode = true, language = "cpp" } = args;

  try {
    // 1. 分析问题类型和关键词
    const analysisResult = analyzeQuestion(question, context);

    // 2. 实时搜索 Axmol 资源
    const searchResults = await searchAxmolResources(
      analysisResult.searchTerms,
      analysisResult.categories,
      language
    );

    // 3. 生成基于实时数据的答案
    const answer = await generateRealtimeAnswer({
      question,
      context,
      analysisResult,
      searchResults,
      includeCode,
      language,
    });

    return {
      content: [
        {
          type: "text",
          text: answer,
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `处理问题时出现错误: ${error instanceof Error ? error.message : String(error)}`,
        },
      ],
    };
  }
}

/**
 * 分析问题类型和提取关键词
 */
function analyzeQuestion(question: string, context: string) {
  const questionLower = question.toLowerCase();
  const contextLower = context.toLowerCase();
  const combined = `${questionLower} ${contextLower}`;

  // 问题类型分类
  const categories = {
    sprite: ["精灵", "sprite", "渲染", "图像", "贴图", "texture", "位图", "bitmap", "点阵图", "bmp"],
    animation: ["动画", "animation", "帧动画", "补间", "tween", "action"],
    physics: ["物理", "physics", "碰撞", "collision", "刚体", "body"],
    ui: ["界面", "ui", "按钮", "button", "标签", "label", "布局", "layout", "字体", "font", "点阵字体", "bitmap font"],
    audio: ["音频", "audio", "声音", "sound", "音乐", "music"],
    scene: ["场景", "scene", "层", "layer", "节点", "node"],
    input: [
      "输入",
      "input",
      "触摸",
      "touch",
      "键盘",
      "keyboard",
      "鼠标",
      "mouse",
    ],
    platform: [
      "平台",
      "platform",
      "跨平台",
      "ios",
      "android",
      "windows",
      "mac",
    ],
    shader: ["着色器", "shader", "渲染", "effect", "特效"],
    particle: ["粒子", "particle", "特效", "effect"],
    "3d": ["3d", "三维", "立体", "3D", "camera", "摄像机", "mesh", "模型", "光照", "lighting"],
    network: ["网络", "network", "http", "socket"],
    file: ["文件", "file", "io", "读取", "写入"],
    memory: ["内存", "memory", "性能", "performance", "优化"],
    build: ["编译", "build", "构建", "cmake", "配置"],
  };

  const detectedCategories = [];
  const searchTerms = [];

  // 检测问题类别
  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => combined.includes(keyword))) {
      detectedCategories.push(category);
      searchTerms.push(...keywords.filter(k => combined.includes(k)));
    }
  }

  // 提取具体的技术关键词
  const technicalTerms = extractTechnicalTerms(question);
  searchTerms.push(...technicalTerms);

  // 如果没有找到关键词，添加问题中的主要词汇
  if (searchTerms.length === 0) {
    const questionWords = question.split(/[\s\u3000\u3001\u3002\uff0c\uff1f\uff01]+/)
      .filter(word => word.length > 1)
      .slice(0, 3);
    searchTerms.push(...questionWords);
  }

  // 将中文关键词转换为英文以提高搜索效果
  const englishSearchTerms = convertChineseToEnglish(searchTerms);
  searchTerms.push(...englishSearchTerms);

  return {
    categories: detectedCategories,
    searchTerms: [...new Set(searchTerms)], // 去重
    questionType: determineQuestionType(question),
    priority: determinePriority(detectedCategories),
  };
}

/**
 * 提取技术关键词
 */
function extractTechnicalTerms(text: string): string[] {
  const terms: string[] = [];

  // 中文到英文的关键词映射
  const keywordMap: { [key: string]: string } = {
    "精灵": "sprite",
    "节点": "node",
    "场景": "scene",
    "动作": "action",
    "动画": "animation",
    "纹理": "texture",
    "物理": "physics",
    "碰撞": "collision",
    "界面": "ui",
    "按钮": "button",
    "标签": "label",
    "层": "layer",
    "导演": "director",
    "调度器": "scheduler",
    "事件": "event",
    "触摸": "touch",
    "音频": "audio",
    "声音": "sound",
    "粒子": "particle",
    "着色器": "shader",
    "渲染": "render",
    "摄像机": "camera",
    "光照": "light",
    "三维": "3d",
    "网格": "mesh",
    "模型": "model",
    "骨骼": "bone",
    "创建": "create",
    "初始化": "init",
    "更新": "update",
    "绘制": "draw",
    "加载": "load",
    "保存": "save"
  };

  // 检查中文关键词
  for (const [chinese, english] of Object.entries(keywordMap)) {
    if (text.includes(chinese)) {
      terms.push(english);
    }
  }

  // 检查英文关键词
  const englishPatterns = [
    /\b(create|init|update|render|draw|load|save|play|stop|pause|resume)\w*/gi,
    /\b(Vec2|Vec3|Size|Rect|Color|Node|Sprite|Scene|Layer)\b/gi,
    /\b(EventListener|Action|Animation|PhysicsBody|PhysicsWorld)\b/gi,
    /\b(sprite|node|scene|action|animation|texture|physics|ui|button|label)\b/gi,
  ];

  englishPatterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      terms.push(...matches.map(m => m.toLowerCase()));
    }
  });

  return [...new Set(terms)]; // 去重
}

/**
 * 将中文关键词转换为英文以提高搜索效果
 */
function convertChineseToEnglish(terms: string[]): string[] {
  const chineseToEnglish: { [key: string]: string[] } = {
    // 基础概念
    "精灵": ["sprite"],
    "节点": ["node"],
    "场景": ["scene"],
    "层": ["layer"],
    "导演": ["director"],

    // 动画和动作
    "动画": ["animation", "animate"],
    "动作": ["action"],
    "补间": ["tween"],
    "帧动画": ["frame animation", "sprite animation"],
    "骨骼动画": ["skeletal animation", "bone animation"],

    // 渲染和图形
    "渲染": ["render", "rendering"],
    "纹理": ["texture"],
    "材质": ["material"],
    "着色器": ["shader"],
    "光照": ["lighting", "light"],
    "摄像机": ["camera"],
    "粒子": ["particle"],

    // 物理系统
    "物理": ["physics"],
    "碰撞": ["collision"],
    "刚体": ["rigid body", "physics body"],
    "重力": ["gravity"],

    // 用户界面
    "界面": ["ui", "user interface"],
    "按钮": ["button"],
    "标签": ["label"],
    "文本": ["text"],
    "输入框": ["editbox", "text field"],
    "滚动视图": ["scroll view"],
    "布局": ["layout"],

    // 音频
    "音频": ["audio"],
    "声音": ["sound"],
    "音乐": ["music"],
    "音效": ["sound effect"],

    // 输入处理
    "触摸": ["touch"],
    "点击": ["click", "tap"],
    "手势": ["gesture"],
    "键盘": ["keyboard"],
    "鼠标": ["mouse"],

    // 3D相关
    "三维": ["3d", "three dimensional"],
    "模型": ["model", "3d model"],
    "网格": ["mesh"],
    "顶点": ["vertex"],
    "面": ["face", "polygon"],

    // 文件和资源
    "加载": ["load", "loading"],
    "保存": ["save"],
    "资源": ["resource", "asset"],
    "图片": ["image", "picture"],
    "文件": ["file"],

    // 开发相关
    "创建": ["create"],
    "初始化": ["init", "initialize"],
    "更新": ["update"],
    "绘制": ["draw"],
    "销毁": ["destroy"],
    "释放": ["release"],

    // 性能和优化
    "性能": ["performance"],
    "优化": ["optimization", "optimize"],
    "内存": ["memory"],
    "帧率": ["fps", "frame rate"],
    "批处理": ["batch", "batching"],

    // 平台相关
    "平台": ["platform"],
    "安卓": ["android"],
    "苹果": ["ios", "iphone"],
    "窗口": ["windows"],
    "网页": ["web", "html5"],

    // 常见问题
    "错误": ["error"],
    "问题": ["issue", "problem"],
    "调试": ["debug"],
    "崩溃": ["crash"],
    "卡顿": ["lag", "stutter"]
  };

  const englishTerms: string[] = [];

  terms.forEach(term => {
    // 检查是否包含中文字符
    if (/[\u4e00-\u9fff]/.test(term)) {
      // 查找对应的英文词汇
      for (const [chinese, englishArray] of Object.entries(chineseToEnglish)) {
        if (term.includes(chinese)) {
          englishTerms.push(...englishArray);
        }
      }
    }
  });

  return [...new Set(englishTerms)]; // 去重
}

// 测试函数已移除

/**
 * 按来源类型分组网络搜索结果
 */
function groupWebResultsBySource(webResults: any[]): any {
  const grouped: {
    official: any[];
    tutorial: any[];
    community: any[];
    cocos2d: any[];
    other: any[];
  } = {
    official: [],
    tutorial: [],
    community: [],
    cocos2d: [],
    other: []
  };

  webResults.forEach((result: any) => {
    switch (result.source) {
      case 'official':
        grouped.official.push(result);
        break;
      case 'tutorial':
        grouped.tutorial.push(result);
        break;
      case 'stackoverflow':
      case 'gamedev':
        grouped.community.push(result);
        break;
      case 'cocos2d':
        grouped.cocos2d.push(result);
        break;
      default:
        grouped.other.push(result);
    }
  });

  return grouped;
}

/**
 * 网络搜索 Axmol 相关资源（备用方案）
 */
async function searchWebResources(searchTerms: string[], categories: string[]): Promise<any[]> {
  const results: any[] = [];

  try {
    // 构建搜索查询，按优先级排序
    const searchQueries = [
      // 第一优先级：官方资源
      `Axmol ${searchTerms.join(' ')} site:github.com/axmolengine`,

      // 第二优先级：技术博客和教程
      `Axmol ${searchTerms.join(' ')} tutorial blog`,

      // 第三优先级：游戏开发社区
      `Axmol ${searchTerms.join(' ')} site:stackoverflow.com OR site:gamedev.net`,

      // 第四优先级：Cocos2d-x 相关（因为 Axmol 基于 Cocos2d-x）
      `Cocos2d-x ${searchTerms.join(' ')} ${categories.includes('sprite') ? 'sprite' : ''}`
    ];

    // 执行多个搜索查询
    for (let i = 0; i < searchQueries.length && results.length < 8; i++) {
      const query = searchQueries[i];
      console.log(`🔍 网络搜索 (优先级${i+1}): ${query}`);

      try {
        // 使用 web-search 工具
        const searchResponse = await webSearch(query, Math.min(3, 8 - results.length));

        if (searchResponse && searchResponse.length > 0) {
          searchResponse.forEach((item: any) => {
            results.push({
              type: 'web',
              title: item.title,
              url: item.url,
              snippet: item.snippet,
              source: getSourceType(item.url),
              priority: i + 1,
              relevantTerms: searchTerms.filter(term =>
                (item.title + ' ' + item.snippet).toLowerCase().includes(term.toLowerCase())
              )
            });
          });
        }
      } catch (error) {
        console.log(`⚠️ 网络搜索查询失败 (优先级${i+1}):`, error instanceof Error ? error.message : String(error));
      }
    }

  } catch (error) {
    console.log('⚠️ 网络搜索失败:', error instanceof Error ? error.message : String(error));
  }

  return results.sort((a, b) => a.priority - b.priority); // 按优先级排序
}

/**
 * 确定搜索结果的来源类型
 */
function getSourceType(url: string): string {
  if (url.includes('github.com/axmolengine')) return 'official';
  if (url.includes('stackoverflow.com')) return 'stackoverflow';
  if (url.includes('gamedev.net')) return 'gamedev';
  if (url.includes('blog') || url.includes('tutorial')) return 'tutorial';
  if (url.includes('cocos2d')) return 'cocos2d';
  return 'other';
}

/**
 * Web 搜索函数（集成 web-search 工具）
 */
async function webSearch(query: string, numResults: number = 3): Promise<any[]> {
  try {
    console.log(`🌐 执行网络搜索: ${query} (${numResults} 个结果)`);

    // 模拟网络搜索结果，实际环境中这里应该调用真正的搜索API
    // 由于我们在 MCP 服务器内部，无法直接调用其他 MCP 工具
    // 这里提供一个基础的实现框架

    const mockResults = [
      {
        title: `Axmol ${query} - 官方文档`,
        url: "https://github.com/axmolengine/axmol/wiki",
        snippet: `关于 ${query} 的官方文档和说明...`
      },
      {
        title: `${query} 教程 - 游戏开发博客`,
        url: "https://example-blog.com/axmol-tutorial",
        snippet: `详细的 ${query} 开发教程和示例代码...`
      }
    ];

    // 在实际实现中，这里应该是真正的网络搜索调用
    // 例如使用 Google Custom Search API 或其他搜索服务

    return mockResults.slice(0, numResults);

  } catch (error) {
    console.log('⚠️ 网络搜索执行失败:', error instanceof Error ? error.message : String(error));
    return [];
  }
}

/**
 * 确定问题类型
 */
function determineQuestionType(question: string): string {
  const questionLower = question.toLowerCase();

  if (
    questionLower.includes("怎么") ||
    questionLower.includes("如何") ||
    questionLower.includes("how")
  ) {
    return "how-to";
  } else if (questionLower.includes("什么") || questionLower.includes("what")) {
    return "definition";
  } else if (
    questionLower.includes("为什么") ||
    questionLower.includes("why")
  ) {
    return "explanation";
  } else if (
    questionLower.includes("错误") ||
    questionLower.includes("error") ||
    questionLower.includes("问题")
  ) {
    return "troubleshooting";
  } else if (
    questionLower.includes("例子") ||
    questionLower.includes("示例") ||
    questionLower.includes("example")
  ) {
    return "example";
  } else {
    return "general";
  }
}

/**
 * 确定搜索优先级
 */
function determinePriority(categories: string[]): string[] {
  const priorityMap: { [key: string]: number } = {
    sprite: 1,
    scene: 1,
    physics: 2,
    animation: 2,
    ui: 3,
    audio: 3,
    input: 4,
    platform: 5,
  };

  return categories.sort(
    (a, b) => (priorityMap[a] || 10) - (priorityMap[b] || 10)
  );
}

/**
 * 实时搜索 Axmol 资源（Wiki + 源码 + 示例）
 */
async function searchAxmolResources(
  searchTerms: string[],
  categories: string[],
  language: string = "cpp"
): Promise<any> {
  console.log(`🔍 开始搜索 Axmol 资源，关键词数量: ${searchTerms.length}`);

  const results: {
    wiki: any[];
    sourceCode: any[];
    examples: any[];
    headers: any[];
    docs: any[];
    webSearch: any[];
  } = {
    wiki: [],
    sourceCode: [],
    examples: [],
    headers: [],
    docs: [],
    webSearch: []
  };

  try {
    // 并行搜索多个数据源
    const searchPromises = [
      searchWikiPages(searchTerms),
      searchSourceCode(searchTerms, categories, language),
      searchExamples(searchTerms, categories),
      searchHeaders(searchTerms, categories),
      searchDocuments(searchTerms)
    ];

    const [wikiResults, sourceResults, exampleResults, headerResults, docResults] =
      await Promise.allSettled(searchPromises);

    // 处理搜索结果
    if (wikiResults.status === 'fulfilled') results.wiki = wikiResults.value;
    if (sourceResults.status === 'fulfilled') results.sourceCode = sourceResults.value;
    if (exampleResults.status === 'fulfilled') results.examples = exampleResults.value;
    if (headerResults.status === 'fulfilled') results.headers = headerResults.value;
    if (docResults.status === 'fulfilled') results.docs = docResults.value;

    console.log(`✅ 官方资源搜索完成: Wiki(${results.wiki.length}), 源码(${results.sourceCode.length}), 示例(${results.examples.length}), 头文件(${results.headers.length}), 文档(${results.docs.length})`);

    // 如果官方资源搜索结果不足，启动网络搜索作为备用
    const totalOfficialResults = Object.values(results).reduce((sum: number, arr: unknown) => sum + (Array.isArray(arr) ? arr.length : 0), 0);

    if (totalOfficialResults < 10) {
      console.log('🌐 官方资源不足，启动网络搜索备用方案...');
      try {
        const webResults = await searchWebResources(searchTerms, categories);
        results.webSearch = webResults;
        console.log(`✅ 网络搜索完成: 找到 ${webResults.length} 个相关资源`);
      } catch (error) {
        console.log('⚠️ 网络搜索也失败了:', error instanceof Error ? error.message : String(error));
        results.webSearch = [];
      }
    } else {
      results.webSearch = [];
    }

  } catch (error) {
    console.error('❌ 搜索过程中出现错误:', error);
  }

  return results;
}

/**
 * 搜索 Wiki 页面
 */
async function searchWikiPages(searchTerms: string[]): Promise<any[]> {
  const wikiPages = [
    "Getting-Started", "Sprite", "Animation", "Physics", "Actions",
    "Scene-Management", "UI-System", "Audio", "Input-Handling",
    "Platform-Specific", "Performance", "Troubleshooting", "3D",
    "Shaders", "Particles", "Networking", "File-IO", "Memory-Management"
  ];

  const results = [];

  for (const page of wikiPages) {
    try {
      const url = `${AXMOL_WIKI_BASE}/${page}`;
      const response = await axios.get(url, {
        timeout: 8000,
        headers: WIKI_HEADERS
      });
      const $ = cheerio.load(response.data);

      const content = $("article, .markdown-body, .wiki-body").text().trim();
      const title = $("h1, .page-title").first().text().trim() || page;

      // 检查是否包含搜索关键词
      const text = `${title} ${content}`.toLowerCase();
      const relevantTerms = searchTerms.filter(term =>
        text.includes(term.toLowerCase())
      );

      if (relevantTerms.length > 0) {
        results.push({
          type: 'wiki',
          title,
          url,
          content: content.substring(0, 3000),
          relevantTerms,
          score: relevantTerms.length
        });
      }
    } catch (error) {
      console.log(`⚠️ Wiki页面 ${page} 搜索失败:`, error instanceof Error ? error.message : String(error));
    }
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索源代码
 */
async function searchSourceCode(searchTerms: string[], _categories: string[], _language: string): Promise<any[]> {
  const results = [];

  try {
    // 由于 GitHub 代码搜索需要认证，我们改为直接搜索特定文件
    console.log('🔍 搜索源码文件...');

    // 搜索核心头文件
    const coreFiles = [
      'core/2d/Sprite.h',
      'core/2d/Node.h',
      'core/2d/Scene.h',
      'core/base/Director.h'
    ];

    for (const filePath of coreFiles) {
      try {
        const fileUrl = `${GITHUB_RAW_BASE}/${AXMOL_REPO}/dev/${filePath}`;
        const response = await axios.get(fileUrl, { timeout: 5000 });

        const content = response.data;
        const lines = content.split('\n');

        // 查找相关代码段
        const relevantLines = [];
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].toLowerCase();
          if (searchTerms.some(term => line.includes(term.toLowerCase()))) {
            // 获取上下文（前后5行）
            const start = Math.max(0, i - 5);
            const end = Math.min(lines.length, i + 6);
            relevantLines.push({
              lineNumber: i + 1,
              context: lines.slice(start, end).join('\n'),
              matchedLine: lines[i]
            });
          }
        }

        if (relevantLines.length > 0) {
          results.push({
            type: 'source',
            filename: filePath.split('/').pop(),
            path: filePath,
            url: `https://github.com/${AXMOL_REPO}/blob/dev/${filePath}`,
            repository: AXMOL_REPO,
            relevantCode: relevantLines.slice(0, 3), // 限制数量
            score: relevantLines.length
          });
        }
      } catch (error) {
        console.log(`⚠️ 获取源码文件失败: ${filePath}`);
      }
    }
  } catch (error) {
    console.log('⚠️ GitHub代码搜索失败:', error instanceof Error ? error.message : String(error));
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索示例代码
 */
async function searchExamples(searchTerms: string[], categories: string[]): Promise<any[]> {
  const results = [];
  const examplePaths = [
    'tests/cpp-tests',
    'tests/lua-tests',
    'examples',
    'samples'
  ];

  try {
    for (const path of examplePaths) {
      const contentsUrl = `${GITHUB_API_BASE}/repos/${AXMOL_REPO}/contents/${path}`;

      try {
        const response = await axios.get(contentsUrl, {
          headers: GITHUB_HEADERS,
          timeout: 8000
        });

        const files = response.data.filter((item: any) =>
          item.type === 'file' &&
          (item.name.endsWith('.cpp') || item.name.endsWith('.h') || item.name.endsWith('.lua'))
        );

        for (const file of files.slice(0, 5)) {
          try {
            const fileResponse = await axios.get(file.download_url, { timeout: 5000 });
            const content = fileResponse.data;

            // 检查文件是否包含相关关键词
            const contentLower = content.toLowerCase();
            const relevantTerms = searchTerms.filter(term =>
              contentLower.includes(term.toLowerCase())
            );

            if (relevantTerms.length > 0) {
              // 提取相关代码段
              const lines = content.split('\n');
              const relevantSections = [];

              for (let i = 0; i < lines.length; i++) {
                const line = lines[i].toLowerCase();
                if (relevantTerms.some(term => line.includes(term.toLowerCase()))) {
                  const start = Math.max(0, i - 3);
                  const end = Math.min(lines.length, i + 8);
                  relevantSections.push({
                    lineNumber: i + 1,
                    code: lines.slice(start, end).join('\n')
                  });
                }
              }

              results.push({
                type: 'example',
                filename: file.name,
                path: file.path,
                url: file.html_url,
                relevantTerms,
                codeSections: relevantSections.slice(0, 2),
                score: relevantTerms.length
              });
            }
          } catch (error) {
            console.log(`⚠️ 获取示例文件失败: ${file.name}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ 获取示例目录失败: ${path}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 示例搜索失败:', error instanceof Error ? error.message : String(error));
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索头文件（API定义）
 */
async function searchHeaders(searchTerms: string[], categories: string[]): Promise<any[]> {
  const results = [];
  const headerPaths = [
    'core/2d',
    'core/3d',
    'core/audio',
    'core/base',
    'core/physics',
    'core/ui',
    'core/renderer'
  ];

  try {
    for (const path of headerPaths) {
      const contentsUrl = `${GITHUB_API_BASE}/repos/${AXMOL_REPO}/contents/${path}`;

      try {
        const response = await axios.get(contentsUrl, {
          headers: GITHUB_HEADERS,
          timeout: 8000
        });

        const headerFiles = response.data.filter((item: any) =>
          item.type === 'file' && item.name.endsWith('.h')
        );

        for (const file of headerFiles.slice(0, 3)) {
          try {
            const fileResponse = await axios.get(file.download_url, { timeout: 5000 });
            const content = fileResponse.data;

            const contentLower = content.toLowerCase();
            const relevantTerms = searchTerms.filter(term =>
              contentLower.includes(term.toLowerCase())
            );

            if (relevantTerms.length > 0) {
              // 提取类定义和方法声明
              const apiDefinitions = extractAPIDefinitions(content, relevantTerms);

              if (apiDefinitions.length > 0) {
                results.push({
                  type: 'header',
                  filename: file.name,
                  path: file.path,
                  url: file.html_url,
                  relevantTerms,
                  apiDefinitions,
                  score: relevantTerms.length
                });
              }
            }
          } catch (error) {
            console.log(`⚠️ 获取头文件失败: ${file.name}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ 获取头文件目录失败: ${path}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 头文件搜索失败:', error instanceof Error ? error.message : String(error));
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索文档文件
 */
async function searchDocuments(searchTerms: string[]): Promise<any[]> {
  const results = [];
  const docFiles = ['README.md', 'CHANGELOG.md', 'docs/README.md'];

  try {
    for (const docFile of docFiles) {
      try {
        const fileUrl = `${GITHUB_RAW_BASE}/${AXMOL_REPO}/dev/${docFile}`;
        const response = await axios.get(fileUrl, { timeout: 5000 });
        const content = response.data;

        const contentLower = content.toLowerCase();
        const relevantTerms = searchTerms.filter(term =>
          contentLower.includes(term.toLowerCase())
        );

        if (relevantTerms.length > 0) {
          results.push({
            type: 'document',
            filename: docFile,
            url: `https://github.com/${AXMOL_REPO}/blob/dev/${docFile}`,
            content: content.substring(0, 2000),
            relevantTerms,
            score: relevantTerms.length
          });
        }
      } catch (error) {
        console.log(`⚠️ 获取文档失败: ${docFile}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 文档搜索失败:', error instanceof Error ? error.message : String(error));
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 提取API定义
 */
function extractAPIDefinitions(content: string, searchTerms: string[]): any[] {
  const definitions = [];
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineLower = line.toLowerCase();

    // 检查是否包含搜索关键词
    if (searchTerms.some(term => lineLower.includes(term.toLowerCase()))) {
      // 检查是否是类定义、方法声明等
      if (line.match(/^\s*(class|struct|enum)\s+\w+/) ||
          line.match(/^\s*\w+.*\([^)]*\)\s*[;{]/) ||
          line.match(/^\s*static\s+\w+/) ||
          line.match(/^\s*virtual\s+\w+/)) {

        const start = Math.max(0, i - 2);
        const end = Math.min(lines.length, i + 3);

        definitions.push({
          lineNumber: i + 1,
          definition: lines.slice(start, end).join('\n'),
          type: getDefinitionType(line)
        });
      }
    }
  }

  return definitions.slice(0, 5); // 限制数量
}

/**
 * 获取定义类型
 */
function getDefinitionType(line: string): string {
  if (line.includes('class ')) return 'class';
  if (line.includes('struct ')) return 'struct';
  if (line.includes('enum ')) return 'enum';
  if (line.includes('static ')) return 'static_method';
  if (line.includes('virtual ')) return 'virtual_method';
  if (line.match(/\([^)]*\)/)) return 'method';
  return 'other';
}

/**
 * 基于实时搜索结果生成答案
 */
async function generateRealtimeAnswer(params: {
  question: string;
  context: string;
  analysisResult: any;
  searchResults: any;
  includeCode: boolean;
  language: string;
}): Promise<string> {
  const {
    question,
    context,
    analysisResult,
    searchResults,
    includeCode,
    language,
  } = params;

  const answer = [];

  // 答案标题
  answer.push(`# Axmol 实时开发解答\n`);
  answer.push(`**问题**: ${question}\n`);
  if (context) {
    answer.push(`**上下文**: ${context}\n`);
  }
  answer.push(`**搜索时间**: ${new Date().toLocaleString()}\n`);
  answer.push(`\n---\n\n`);

  // 问题分析
  answer.push(`## 🔍 问题分析\n`);
  answer.push(`- **问题类型**: ${analysisResult.questionType}\n`);
  answer.push(`- **相关领域**: ${analysisResult.categories.join(", ") || "通用"}\n`);
  answer.push(`- **搜索关键词**: ${analysisResult.searchTerms.slice(0, 5).join(", ")}\n\n`);

  // 搜索结果统计
  const totalResults = Object.values(searchResults).reduce((sum: number, arr: unknown) => sum + (Array.isArray(arr) ? arr.length : 0), 0);
  answer.push(`## 📊 搜索结果统计\n`);
  answer.push(`- **Wiki文档**: ${searchResults.wiki.length} 个页面\n`);
  answer.push(`- **源代码**: ${searchResults.sourceCode.length} 个文件\n`);
  answer.push(`- **示例代码**: ${searchResults.examples.length} 个示例\n`);
  answer.push(`- **API头文件**: ${searchResults.headers.length} 个文件\n`);
  answer.push(`- **文档资料**: ${searchResults.docs.length} 个文档\n`);
  if (searchResults.webSearch && searchResults.webSearch.length > 0) {
    answer.push(`- **网络资源**: ${searchResults.webSearch.length} 个相关资源\n`);
  }
  answer.push(`- **总计**: ${totalResults} 个相关资源\n\n`);

  if (totalResults === 0) {
    answer.push(`## 🔍 基于问题分析的建议\n`);
    answer.push(`虽然实时搜索遇到了网络问题，但基于您的问题分析，我可以提供以下建议：\n\n`);

    // 基于问题类型提供建议
    if (analysisResult.categories.includes("sprite") || question.includes("精灵")) {
      answer.push(`### 关于精灵创建\n`);
      answer.push(`Axmol 中创建精灵的基本步骤：\n`);
      answer.push(`1. 使用 \`Sprite::create()\` 方法\n`);
      answer.push(`2. 设置位置和属性\n`);
      answer.push(`3. 添加到场景中\n\n`);

      if (includeCode) {
        answer.push(`**代码示例**:\n`);
        answer.push(`\`\`\`cpp\n`);
        answer.push(`// 创建精灵\n`);
        answer.push(`auto sprite = Sprite::create("player.png");\n`);
        answer.push(`sprite->setPosition(Vec2(100, 100));\n`);
        answer.push(`this->addChild(sprite);\n`);
        answer.push(`\`\`\`\n\n`);
      }
    }

    answer.push(`## 📚 推荐资源\n`);
    answer.push(`由于网络搜索暂时不可用，建议您直接访问：\n`);
    answer.push(`- 🔗 [Axmol GitHub 主页](https://github.com/axmolengine/axmol)\n`);
    answer.push(`- 📚 [官方 Wiki](https://github.com/axmolengine/axmol/wiki)\n`);
    answer.push(`- 💻 [源码示例](https://github.com/axmolengine/axmol/tree/dev/tests/cpp-tests)\n`);
    answer.push(`- 🐛 [问题反馈](https://github.com/axmolengine/axmol/issues)\n\n`);

    answer.push(`## 🔧 故障排除\n`);
    answer.push(`如果您经常遇到网络搜索问题，可能的原因：\n`);
    answer.push(`1. **网络连接**: 检查是否能正常访问 GitHub\n`);
    answer.push(`2. **防火墙设置**: 确保 Node.js 可以进行网络请求\n`);
    answer.push(`3. **代理配置**: 如果使用代理，请确保配置正确\n`);
    answer.push(`4. **DNS 解析**: 尝试使用不同的 DNS 服务器\n\n`);

    return answer.join("");
  }

  // Wiki 文档结果
  if (searchResults.wiki.length > 0) {
    answer.push(`## 📚 官方Wiki文档\n`);
    searchResults.wiki.slice(0, 3).forEach((wiki: any) => {
      answer.push(`### ${wiki.title}\n`);
      answer.push(`**链接**: [${wiki.url}](${wiki.url})\n`);
      answer.push(`**相关关键词**: ${wiki.relevantTerms.join(', ')}\n`);
      answer.push(`**内容摘要**:\n${wiki.content.substring(0, 500)}...\n\n`);
    });
  }

  // 源代码结果
  if (searchResults.sourceCode.length > 0 && includeCode) {
    answer.push(`## 💻 最新源代码示例\n`);
    searchResults.sourceCode.slice(0, 2).forEach((source: any) => {
      answer.push(`### ${source.filename}\n`);
      answer.push(`**文件路径**: \`${source.path}\`\n`);
      answer.push(`**GitHub链接**: [查看源码](${source.url})\n`);
      answer.push(`**相关代码段**:\n`);
      source.relevantCode.forEach((code: any) => {
        answer.push(`\`\`\`${language}\n${code.context}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // 示例代码结果
  if (searchResults.examples.length > 0 && includeCode) {
    answer.push(`## 🎯 官方示例代码\n`);
    searchResults.examples.slice(0, 2).forEach((example: any) => {
      answer.push(`### ${example.filename}\n`);
      answer.push(`**示例路径**: \`${example.path}\`\n`);
      answer.push(`**GitHub链接**: [查看示例](${example.url})\n`);
      answer.push(`**相关关键词**: ${example.relevantTerms.join(', ')}\n`);
      answer.push(`**代码片段**:\n`);
      example.codeSections.forEach((section: any) => {
        answer.push(`\`\`\`${language}\n${section.code}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // API 头文件结果
  if (searchResults.headers.length > 0) {
    answer.push(`## 🔧 API 定义\n`);
    searchResults.headers.slice(0, 2).forEach((header: any) => {
      answer.push(`### ${header.filename}\n`);
      answer.push(`**头文件路径**: \`${header.path}\`\n`);
      answer.push(`**GitHub链接**: [查看头文件](${header.url})\n`);
      answer.push(`**API定义**:\n`);
      header.apiDefinitions.forEach((api: any) => {
        answer.push(`**${api.type}** (第${api.lineNumber}行):\n`);
        answer.push(`\`\`\`cpp\n${api.definition}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // 文档资料结果
  if (searchResults.docs.length > 0) {
    answer.push(`## 📖 相关文档\n`);
    searchResults.docs.forEach((doc: any) => {
      answer.push(`### ${doc.filename}\n`);
      answer.push(`**文档链接**: [查看文档](${doc.url})\n`);
      answer.push(`**相关关键词**: ${doc.relevantTerms.join(', ')}\n`);
      answer.push(`**内容摘要**:\n${doc.content.substring(0, 400)}...\n\n`);
    });
  }

  // 网络搜索结果
  if (searchResults.webSearch && searchResults.webSearch.length > 0) {
    answer.push(`## 🌐 网络搜索结果\n`);
    answer.push(`> 💡 **说明**: 以下是从互联网搜索到的相关资源，按可信度排序。官方资源优先，第三方资料仅供参考。\n\n`);

    // 按来源类型分组显示
    const groupedResults = groupWebResultsBySource(searchResults.webSearch);

    if (groupedResults.official.length > 0) {
      answer.push(`### 🏛️ 官方资源\n`);
      groupedResults.official.forEach((result: any) => {
        answer.push(`- **[${result.title}](${result.url})**\n`);
        answer.push(`  ${result.snippet}\n\n`);
      });
    }

    if (groupedResults.tutorial.length > 0) {
      answer.push(`### 📚 教程和博客\n`);
      answer.push(`> ⚠️ **注意**: 第三方教程可能基于较旧版本，请注意时效性。\n\n`);
      groupedResults.tutorial.forEach((result: any) => {
        answer.push(`- **[${result.title}](${result.url})**\n`);
        answer.push(`  ${result.snippet}\n\n`);
      });
    }

    if (groupedResults.community.length > 0) {
      answer.push(`### 💬 社区讨论\n`);
      answer.push(`> ℹ️ **提示**: 社区问答可能包含多种解决方案，建议结合官方文档验证。\n\n`);
      groupedResults.community.forEach((result: any) => {
        answer.push(`- **[${result.title}](${result.url})**\n`);
        answer.push(`  ${result.snippet}\n\n`);
      });
    }

    if (groupedResults.cocos2d.length > 0) {
      answer.push(`### 🔄 Cocos2d-x 相关\n`);
      answer.push(`> 📝 **说明**: Axmol 基于 Cocos2d-x，以下资料可能需要适配。\n\n`);
      groupedResults.cocos2d.forEach((result: any) => {
        answer.push(`- **[${result.title}](${result.url})**\n`);
        answer.push(`  ${result.snippet}\n\n`);
      });
    }
  }

  // 实用建议
  answer.push(`## 💡 实用建议\n`);
  answer.push(`基于最新搜索结果的建议：\n\n`);

  if (searchResults.wiki.length > 0) {
    answer.push(`- 📚 **优先参考Wiki文档**: 找到了 ${searchResults.wiki.length} 个相关Wiki页面，建议详细阅读\n`);
  }

  if (searchResults.sourceCode.length > 0) {
    answer.push(`- 💻 **参考源码实现**: 找到了 ${searchResults.sourceCode.length} 个相关源码文件，可以了解最新实现方式\n`);
  }

  if (searchResults.examples.length > 0) {
    answer.push(`- 🎯 **学习官方示例**: 找到了 ${searchResults.examples.length} 个官方示例，建议运行和学习\n`);
  }

  if (searchResults.headers.length > 0) {
    answer.push(`- 🔧 **查看API定义**: 找到了 ${searchResults.headers.length} 个相关头文件，了解最新API\n`);
  }

  if (searchResults.webSearch && searchResults.webSearch.length > 0) {
    answer.push(`- 🌐 **参考网络资源**: 找到了 ${searchResults.webSearch.length} 个相关网络资源，包含教程和社区讨论\n`);
    answer.push(`- ⚠️ **注意时效性**: 第三方资料可能基于较旧版本，建议优先参考官方资源\n`);
  }

  answer.push(`\n**获取最新信息**:\n`);
  answer.push(`- 🔗 [Axmol GitHub主页](https://github.com/axmolengine/axmol)\n`);
  answer.push(`- 📚 [官方Wiki](https://github.com/axmolengine/axmol/wiki)\n`);
  answer.push(`- 🐛 [问题反馈](https://github.com/axmolengine/axmol/issues)\n`);
  answer.push(`- 💬 [社区讨论](https://github.com/axmolengine/axmol/discussions)\n\n`);

  answer.push(`---\n`);
  answer.push(`*本回答基于 ${new Date().toLocaleString()} 的实时搜索结果生成*\n`);

  return answer.join("");
}

// 本地知识库函数已移除 - 现在完全依赖实时搜索

// 所有本地知识库函数已移除 - 现在完全基于实时搜索结果生成答案

// 本地答案生成函数已全部移除

// 本地代码示例生成函数已全部移除

// 相关建议生成函数已移除 - 建议现在基于实时搜索结果生成

// 启动服务器
main().catch(error => {
  console.error("服务器启动失败:", error);
  process.exit(1);
});
