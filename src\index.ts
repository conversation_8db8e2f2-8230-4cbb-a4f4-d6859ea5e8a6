#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import axios from "axios";
import * as cheerio from "cheerio";

// Axmol 相关 URL 配置
const AXMOL_REPO = "axmolengine/axmol";
const AXMOL_WIKI_BASE = "https://github.com/axmolengine/axmol/wiki";
const GITHUB_API_BASE = "https://api.github.com";
const GITHUB_RAW_BASE = "https://raw.githubusercontent.com";

// GitHub API 配置
const GITHUB_HEADERS = {
  'Accept': 'application/vnd.github.v3+json',
  'User-Agent': 'Axmol-MCP-Assistant'
};

/**
 * Axmol MCP 服务器主入口
 */
async function main() {
  const server = new Server({
    name: "axmol-mcp-server",
    version: "1.0.0",
  });

  // 注册统一的智能问答工具
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    return {
      tools: [
        {
          name: "axmol_smart_assistant",
          description:
            "Axmol 实时智能助手 - 实时获取 Axmol 引擎最新源码、文档和示例，提供最新最全的开发解决方案",
          inputSchema: {
            type: "object",
            properties: {
              question: {
                type: "string",
                description: "您想要询问的 Axmol 开发问题（支持中文和英文）",
              },
              context: {
                type: "string",
                description:
                  "可选的上下文信息，比如您正在开发的功能类型、目标平台等",
                default: "",
              },
              includeCode: {
                type: "boolean",
                description: "是否需要包含代码示例",
                default: true,
              },
              language: {
                type: "string",
                enum: ["cpp", "lua", "both"],
                description: "代码示例语言偏好",
                default: "cpp",
              },
            },
            required: ["question"],
          },
        },
      ],
    };
  });

  // 注册工具调用处理器
  server.setRequestHandler(CallToolRequestSchema, async request => {
    const { name, arguments: args } = request.params;

    try {
      switch (name) {
        case "axmol_smart_assistant":
          return await handleSmartAssistant(args as any);
        default:
          throw new Error(`未知工具: ${name}`);
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `错误: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
      };
    }
  });

  // 启动服务器
  const transport = new StdioServerTransport();
  await server.connect(transport);
}

/**
 * 智能助手主处理函数
 */
async function handleSmartAssistant(args: {
  question: string;
  context?: string;
  includeCode?: boolean;
  language?: string;
}): Promise<any> {
  const { question, context = "", includeCode = true, language = "cpp" } = args;

  try {
    // 1. 分析问题类型和关键词
    const analysisResult = analyzeQuestion(question, context);

    // 2. 实时搜索 Axmol 资源
    const searchResults = await searchAxmolResources(
      analysisResult.searchTerms,
      analysisResult.categories,
      language
    );

    // 3. 生成基于实时数据的答案
    const answer = await generateRealtimeAnswer({
      question,
      context,
      analysisResult,
      searchResults,
      includeCode,
      language,
    });

    return {
      content: [
        {
          type: "text",
          text: answer,
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: "text",
          text: `处理问题时出现错误: ${error instanceof Error ? error.message : String(error)}`,
        },
      ],
    };
  }
}

/**
 * 分析问题类型和提取关键词
 */
function analyzeQuestion(question: string, context: string) {
  const questionLower = question.toLowerCase();
  const contextLower = context.toLowerCase();
  const combined = `${questionLower} ${contextLower}`;

  // 问题类型分类
  const categories = {
    sprite: ["精灵", "sprite", "渲染", "图像", "贴图", "texture", "位图", "bitmap", "点阵图", "bmp"],
    animation: ["动画", "animation", "帧动画", "补间", "tween", "action"],
    physics: ["物理", "physics", "碰撞", "collision", "刚体", "body"],
    ui: ["界面", "ui", "按钮", "button", "标签", "label", "布局", "layout", "字体", "font", "点阵字体", "bitmap font"],
    audio: ["音频", "audio", "声音", "sound", "音乐", "music"],
    scene: ["场景", "scene", "层", "layer", "节点", "node"],
    input: [
      "输入",
      "input",
      "触摸",
      "touch",
      "键盘",
      "keyboard",
      "鼠标",
      "mouse",
    ],
    platform: [
      "平台",
      "platform",
      "跨平台",
      "ios",
      "android",
      "windows",
      "mac",
    ],
    shader: ["着色器", "shader", "渲染", "effect", "特效"],
    particle: ["粒子", "particle", "特效", "effect"],
    "3d": ["3d", "三维", "立体", "3D", "camera", "摄像机", "mesh", "模型", "光照", "lighting"],
    network: ["网络", "network", "http", "socket"],
    file: ["文件", "file", "io", "读取", "写入"],
    memory: ["内存", "memory", "性能", "performance", "优化"],
    build: ["编译", "build", "构建", "cmake", "配置"],
  };

  const detectedCategories = [];
  const searchTerms = [];

  // 检测问题类别
  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => combined.includes(keyword))) {
      detectedCategories.push(category);
      searchTerms.push(...keywords.filter(k => combined.includes(k)));
    }
  }

  // 提取具体的技术关键词
  const technicalTerms = extractTechnicalTerms(question);
  searchTerms.push(...technicalTerms);

  return {
    categories: detectedCategories,
    searchTerms: [...new Set(searchTerms)], // 去重
    questionType: determineQuestionType(question),
    priority: determinePriority(detectedCategories),
  };
}

/**
 * 提取技术关键词
 */
function extractTechnicalTerms(text: string): string[] {
  const terms: string[] = [];
  const patterns = [
    /\b(create|init|update|render|draw|load|save|play|stop|pause|resume)\w*/gi,
    /\b(Vec2|Vec3|Size|Rect|Color|Node|Sprite|Scene|Layer)\b/gi,
    /\b(EventListener|Action|Animation|PhysicsBody|PhysicsWorld)\b/gi,
  ];

  patterns.forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      terms.push(...matches);
    }
  });

  return terms;
}

/**
 * 确定问题类型
 */
function determineQuestionType(question: string): string {
  const questionLower = question.toLowerCase();

  if (
    questionLower.includes("怎么") ||
    questionLower.includes("如何") ||
    questionLower.includes("how")
  ) {
    return "how-to";
  } else if (questionLower.includes("什么") || questionLower.includes("what")) {
    return "definition";
  } else if (
    questionLower.includes("为什么") ||
    questionLower.includes("why")
  ) {
    return "explanation";
  } else if (
    questionLower.includes("错误") ||
    questionLower.includes("error") ||
    questionLower.includes("问题")
  ) {
    return "troubleshooting";
  } else if (
    questionLower.includes("例子") ||
    questionLower.includes("示例") ||
    questionLower.includes("example")
  ) {
    return "example";
  } else {
    return "general";
  }
}

/**
 * 确定搜索优先级
 */
function determinePriority(categories: string[]): string[] {
  const priorityMap: { [key: string]: number } = {
    sprite: 1,
    scene: 1,
    physics: 2,
    animation: 2,
    ui: 3,
    audio: 3,
    input: 4,
    platform: 5,
  };

  return categories.sort(
    (a, b) => (priorityMap[a] || 10) - (priorityMap[b] || 10)
  );
}

/**
 * 实时搜索 Axmol 资源（Wiki + 源码 + 示例）
 */
async function searchAxmolResources(
  searchTerms: string[],
  categories: string[],
  language: string = "cpp"
): Promise<any> {
  console.log(`🔍 开始搜索 Axmol 资源，关键词: ${searchTerms.join(', ')}`);

  const results = {
    wiki: [],
    sourceCode: [],
    examples: [],
    headers: [],
    docs: []
  };

  try {
    // 并行搜索多个数据源
    const searchPromises = [
      searchWikiPages(searchTerms),
      searchSourceCode(searchTerms, categories, language),
      searchExamples(searchTerms, categories),
      searchHeaders(searchTerms, categories),
      searchDocuments(searchTerms)
    ];

    const [wikiResults, sourceResults, exampleResults, headerResults, docResults] =
      await Promise.allSettled(searchPromises);

    // 处理搜索结果
    if (wikiResults.status === 'fulfilled') results.wiki = wikiResults.value;
    if (sourceResults.status === 'fulfilled') results.sourceCode = sourceResults.value;
    if (exampleResults.status === 'fulfilled') results.examples = exampleResults.value;
    if (headerResults.status === 'fulfilled') results.headers = headerResults.value;
    if (docResults.status === 'fulfilled') results.docs = docResults.value;

    console.log(`✅ 搜索完成: Wiki(${results.wiki.length}), 源码(${results.sourceCode.length}), 示例(${results.examples.length}), 头文件(${results.headers.length}), 文档(${results.docs.length})`);

  } catch (error) {
    console.error('❌ 搜索过程中出现错误:', error);
  }

  return results;
}

/**
 * 搜索 Wiki 页面
 */
async function searchWikiPages(searchTerms: string[]): Promise<any[]> {
  const wikiPages = [
    "Getting-Started", "Sprite", "Animation", "Physics", "Actions",
    "Scene-Management", "UI-System", "Audio", "Input-Handling",
    "Platform-Specific", "Performance", "Troubleshooting", "3D",
    "Shaders", "Particles", "Networking", "File-IO", "Memory-Management"
  ];

  const results = [];

  for (const page of wikiPages) {
    try {
      const url = `${AXMOL_WIKI_BASE}/${page}`;
      const response = await axios.get(url, {
        timeout: 8000,
        headers: GITHUB_HEADERS
      });
      const $ = cheerio.load(response.data);

      const content = $("article, .markdown-body, .wiki-body").text().trim();
      const title = $("h1, .page-title").first().text().trim() || page;

      // 检查是否包含搜索关键词
      const text = `${title} ${content}`.toLowerCase();
      const relevantTerms = searchTerms.filter(term =>
        text.includes(term.toLowerCase())
      );

      if (relevantTerms.length > 0) {
        results.push({
          type: 'wiki',
          title,
          url,
          content: content.substring(0, 3000),
          relevantTerms,
          score: relevantTerms.length
        });
      }
    } catch (error) {
      console.log(`⚠️ Wiki页面 ${page} 搜索失败:`, error.message);
    }
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索源代码
 */
async function searchSourceCode(searchTerms: string[], categories: string[], language: string): Promise<any[]> {
  const results = [];

  try {
    // 构建搜索查询
    const query = searchTerms.slice(0, 3).join(' OR ');
    const searchUrl = `${GITHUB_API_BASE}/search/code?q=${encodeURIComponent(query)}+repo:${AXMOL_REPO}+language:${language === 'lua' ? 'lua' : 'cpp'}`;

    const response = await axios.get(searchUrl, {
      headers: GITHUB_HEADERS,
      timeout: 10000
    });

    const items = response.data.items || [];

    for (const item of items.slice(0, 10)) {
      try {
        // 获取文件内容
        const fileResponse = await axios.get(item.download_url, {
          timeout: 5000
        });

        const content = fileResponse.data;
        const lines = content.split('\n');

        // 查找相关代码段
        const relevantLines = [];
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].toLowerCase();
          if (searchTerms.some(term => line.includes(term.toLowerCase()))) {
            // 获取上下文（前后5行）
            const start = Math.max(0, i - 5);
            const end = Math.min(lines.length, i + 6);
            relevantLines.push({
              lineNumber: i + 1,
              context: lines.slice(start, end).join('\n'),
              matchedLine: lines[i]
            });
          }
        }

        if (relevantLines.length > 0) {
          results.push({
            type: 'source',
            filename: item.name,
            path: item.path,
            url: item.html_url,
            repository: item.repository.full_name,
            relevantCode: relevantLines.slice(0, 3), // 限制数量
            score: relevantLines.length
          });
        }
      } catch (error) {
        console.log(`⚠️ 获取源码文件失败: ${item.name}`);
      }
    }
  } catch (error) {
    console.log('⚠️ GitHub代码搜索失败:', error.message);
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索示例代码
 */
async function searchExamples(searchTerms: string[], categories: string[]): Promise<any[]> {
  const results = [];
  const examplePaths = [
    'tests/cpp-tests',
    'tests/lua-tests',
    'examples',
    'samples'
  ];

  try {
    for (const path of examplePaths) {
      const contentsUrl = `${GITHUB_API_BASE}/repos/${AXMOL_REPO}/contents/${path}`;

      try {
        const response = await axios.get(contentsUrl, {
          headers: GITHUB_HEADERS,
          timeout: 8000
        });

        const files = response.data.filter(item =>
          item.type === 'file' &&
          (item.name.endsWith('.cpp') || item.name.endsWith('.h') || item.name.endsWith('.lua'))
        );

        for (const file of files.slice(0, 5)) {
          try {
            const fileResponse = await axios.get(file.download_url, { timeout: 5000 });
            const content = fileResponse.data;

            // 检查文件是否包含相关关键词
            const contentLower = content.toLowerCase();
            const relevantTerms = searchTerms.filter(term =>
              contentLower.includes(term.toLowerCase())
            );

            if (relevantTerms.length > 0) {
              // 提取相关代码段
              const lines = content.split('\n');
              const relevantSections = [];

              for (let i = 0; i < lines.length; i++) {
                const line = lines[i].toLowerCase();
                if (relevantTerms.some(term => line.includes(term.toLowerCase()))) {
                  const start = Math.max(0, i - 3);
                  const end = Math.min(lines.length, i + 8);
                  relevantSections.push({
                    lineNumber: i + 1,
                    code: lines.slice(start, end).join('\n')
                  });
                }
              }

              results.push({
                type: 'example',
                filename: file.name,
                path: file.path,
                url: file.html_url,
                relevantTerms,
                codeSections: relevantSections.slice(0, 2),
                score: relevantTerms.length
              });
            }
          } catch (error) {
            console.log(`⚠️ 获取示例文件失败: ${file.name}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ 获取示例目录失败: ${path}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 示例搜索失败:', error.message);
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索头文件（API定义）
 */
async function searchHeaders(searchTerms: string[], categories: string[]): Promise<any[]> {
  const results = [];
  const headerPaths = [
    'core/2d',
    'core/3d',
    'core/audio',
    'core/base',
    'core/physics',
    'core/ui',
    'core/renderer'
  ];

  try {
    for (const path of headerPaths) {
      const contentsUrl = `${GITHUB_API_BASE}/repos/${AXMOL_REPO}/contents/${path}`;

      try {
        const response = await axios.get(contentsUrl, {
          headers: GITHUB_HEADERS,
          timeout: 8000
        });

        const headerFiles = response.data.filter(item =>
          item.type === 'file' && item.name.endsWith('.h')
        );

        for (const file of headerFiles.slice(0, 3)) {
          try {
            const fileResponse = await axios.get(file.download_url, { timeout: 5000 });
            const content = fileResponse.data;

            const contentLower = content.toLowerCase();
            const relevantTerms = searchTerms.filter(term =>
              contentLower.includes(term.toLowerCase())
            );

            if (relevantTerms.length > 0) {
              // 提取类定义和方法声明
              const apiDefinitions = extractAPIDefinitions(content, relevantTerms);

              if (apiDefinitions.length > 0) {
                results.push({
                  type: 'header',
                  filename: file.name,
                  path: file.path,
                  url: file.html_url,
                  relevantTerms,
                  apiDefinitions,
                  score: relevantTerms.length
                });
              }
            }
          } catch (error) {
            console.log(`⚠️ 获取头文件失败: ${file.name}`);
          }
        }
      } catch (error) {
        console.log(`⚠️ 获取头文件目录失败: ${path}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 头文件搜索失败:', error.message);
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 搜索文档文件
 */
async function searchDocuments(searchTerms: string[]): Promise<any[]> {
  const results = [];
  const docFiles = ['README.md', 'CHANGELOG.md', 'docs/README.md'];

  try {
    for (const docFile of docFiles) {
      try {
        const fileUrl = `${GITHUB_RAW_BASE}/${AXMOL_REPO}/dev/${docFile}`;
        const response = await axios.get(fileUrl, { timeout: 5000 });
        const content = response.data;

        const contentLower = content.toLowerCase();
        const relevantTerms = searchTerms.filter(term =>
          contentLower.includes(term.toLowerCase())
        );

        if (relevantTerms.length > 0) {
          results.push({
            type: 'document',
            filename: docFile,
            url: `https://github.com/${AXMOL_REPO}/blob/dev/${docFile}`,
            content: content.substring(0, 2000),
            relevantTerms,
            score: relevantTerms.length
          });
        }
      } catch (error) {
        console.log(`⚠️ 获取文档失败: ${docFile}`);
      }
    }
  } catch (error) {
    console.log('⚠️ 文档搜索失败:', error.message);
  }

  return results.sort((a, b) => b.score - a.score);
}

/**
 * 提取API定义
 */
function extractAPIDefinitions(content: string, searchTerms: string[]): any[] {
  const definitions = [];
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineLower = line.toLowerCase();

    // 检查是否包含搜索关键词
    if (searchTerms.some(term => lineLower.includes(term.toLowerCase()))) {
      // 检查是否是类定义、方法声明等
      if (line.match(/^\s*(class|struct|enum)\s+\w+/) ||
          line.match(/^\s*\w+.*\([^)]*\)\s*[;{]/) ||
          line.match(/^\s*static\s+\w+/) ||
          line.match(/^\s*virtual\s+\w+/)) {

        const start = Math.max(0, i - 2);
        const end = Math.min(lines.length, i + 3);

        definitions.push({
          lineNumber: i + 1,
          definition: lines.slice(start, end).join('\n'),
          type: getDefinitionType(line)
        });
      }
    }
  }

  return definitions.slice(0, 5); // 限制数量
}

/**
 * 获取定义类型
 */
function getDefinitionType(line: string): string {
  if (line.includes('class ')) return 'class';
  if (line.includes('struct ')) return 'struct';
  if (line.includes('enum ')) return 'enum';
  if (line.includes('static ')) return 'static_method';
  if (line.includes('virtual ')) return 'virtual_method';
  if (line.match(/\([^)]*\)/)) return 'method';
  return 'other';
}

/**
 * 基于实时搜索结果生成答案
 */
async function generateRealtimeAnswer(params: {
  question: string;
  context: string;
  analysisResult: any;
  searchResults: any;
  includeCode: boolean;
  language: string;
}): Promise<string> {
  const {
    question,
    context,
    analysisResult,
    searchResults,
    includeCode,
    language,
  } = params;

  const answer = [];

  // 答案标题
  answer.push(`# Axmol 实时开发解答\n`);
  answer.push(`**问题**: ${question}\n`);
  if (context) {
    answer.push(`**上下文**: ${context}\n`);
  }
  answer.push(`**搜索时间**: ${new Date().toLocaleString()}\n`);
  answer.push(`\n---\n\n`);

  // 问题分析
  answer.push(`## 🔍 问题分析\n`);
  answer.push(`- **问题类型**: ${analysisResult.questionType}\n`);
  answer.push(`- **相关领域**: ${analysisResult.categories.join(", ") || "通用"}\n`);
  answer.push(`- **搜索关键词**: ${analysisResult.searchTerms.slice(0, 5).join(", ")}\n\n`);

  // 搜索结果统计
  const totalResults = Object.values(searchResults).reduce((sum: number, arr: any[]) => sum + arr.length, 0);
  answer.push(`## 📊 搜索结果统计\n`);
  answer.push(`- **Wiki文档**: ${searchResults.wiki.length} 个页面\n`);
  answer.push(`- **源代码**: ${searchResults.sourceCode.length} 个文件\n`);
  answer.push(`- **示例代码**: ${searchResults.examples.length} 个示例\n`);
  answer.push(`- **API头文件**: ${searchResults.headers.length} 个文件\n`);
  answer.push(`- **文档资料**: ${searchResults.docs.length} 个文档\n`);
  answer.push(`- **总计**: ${totalResults} 个相关资源\n\n`);

  if (totalResults === 0) {
    answer.push(`## ❌ 搜索结果\n`);
    answer.push(`很抱歉，没有找到与您问题直接相关的最新资源。这可能是因为：\n`);
    answer.push(`1. 网络连接问题\n`);
    answer.push(`2. GitHub API 访问限制\n`);
    answer.push(`3. 搜索关键词过于具体\n\n`);
    answer.push(`建议：\n`);
    answer.push(`- 检查网络连接\n`);
    answer.push(`- 尝试使用更通用的关键词\n`);
    answer.push(`- 直接访问 [Axmol GitHub](https://github.com/axmolengine/axmol) 查看最新信息\n\n`);
    return answer.join("");
  }

  // Wiki 文档结果
  if (searchResults.wiki.length > 0) {
    answer.push(`## 📚 官方Wiki文档\n`);
    searchResults.wiki.slice(0, 3).forEach((wiki: any) => {
      answer.push(`### ${wiki.title}\n`);
      answer.push(`**链接**: [${wiki.url}](${wiki.url})\n`);
      answer.push(`**相关关键词**: ${wiki.relevantTerms.join(', ')}\n`);
      answer.push(`**内容摘要**:\n${wiki.content.substring(0, 500)}...\n\n`);
    });
  }

  // 源代码结果
  if (searchResults.sourceCode.length > 0 && includeCode) {
    answer.push(`## 💻 最新源代码示例\n`);
    searchResults.sourceCode.slice(0, 2).forEach((source: any) => {
      answer.push(`### ${source.filename}\n`);
      answer.push(`**文件路径**: \`${source.path}\`\n`);
      answer.push(`**GitHub链接**: [查看源码](${source.url})\n`);
      answer.push(`**相关代码段**:\n`);
      source.relevantCode.forEach((code: any) => {
        answer.push(`\`\`\`${language}\n${code.context}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // 示例代码结果
  if (searchResults.examples.length > 0 && includeCode) {
    answer.push(`## 🎯 官方示例代码\n`);
    searchResults.examples.slice(0, 2).forEach((example: any) => {
      answer.push(`### ${example.filename}\n`);
      answer.push(`**示例路径**: \`${example.path}\`\n`);
      answer.push(`**GitHub链接**: [查看示例](${example.url})\n`);
      answer.push(`**相关关键词**: ${example.relevantTerms.join(', ')}\n`);
      answer.push(`**代码片段**:\n`);
      example.codeSections.forEach((section: any) => {
        answer.push(`\`\`\`${language}\n${section.code}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // API 头文件结果
  if (searchResults.headers.length > 0) {
    answer.push(`## 🔧 API 定义\n`);
    searchResults.headers.slice(0, 2).forEach((header: any) => {
      answer.push(`### ${header.filename}\n`);
      answer.push(`**头文件路径**: \`${header.path}\`\n`);
      answer.push(`**GitHub链接**: [查看头文件](${header.url})\n`);
      answer.push(`**API定义**:\n`);
      header.apiDefinitions.forEach((api: any) => {
        answer.push(`**${api.type}** (第${api.lineNumber}行):\n`);
        answer.push(`\`\`\`cpp\n${api.definition}\n\`\`\`\n`);
      });
      answer.push(`\n`);
    });
  }

  // 文档资料结果
  if (searchResults.docs.length > 0) {
    answer.push(`## 📖 相关文档\n`);
    searchResults.docs.forEach((doc: any) => {
      answer.push(`### ${doc.filename}\n`);
      answer.push(`**文档链接**: [查看文档](${doc.url})\n`);
      answer.push(`**相关关键词**: ${doc.relevantTerms.join(', ')}\n`);
      answer.push(`**内容摘要**:\n${doc.content.substring(0, 400)}...\n\n`);
    });
  }

  // 实用建议
  answer.push(`## 💡 实用建议\n`);
  answer.push(`基于最新搜索结果的建议：\n\n`);

  if (searchResults.wiki.length > 0) {
    answer.push(`- 📚 **优先参考Wiki文档**: 找到了 ${searchResults.wiki.length} 个相关Wiki页面，建议详细阅读\n`);
  }

  if (searchResults.sourceCode.length > 0) {
    answer.push(`- 💻 **参考源码实现**: 找到了 ${searchResults.sourceCode.length} 个相关源码文件，可以了解最新实现方式\n`);
  }

  if (searchResults.examples.length > 0) {
    answer.push(`- 🎯 **学习官方示例**: 找到了 ${searchResults.examples.length} 个官方示例，建议运行和学习\n`);
  }

  if (searchResults.headers.length > 0) {
    answer.push(`- 🔧 **查看API定义**: 找到了 ${searchResults.headers.length} 个相关头文件，了解最新API\n`);
  }

  answer.push(`\n**获取最新信息**:\n`);
  answer.push(`- 🔗 [Axmol GitHub主页](https://github.com/axmolengine/axmol)\n`);
  answer.push(`- 📚 [官方Wiki](https://github.com/axmolengine/axmol/wiki)\n`);
  answer.push(`- 🐛 [问题反馈](https://github.com/axmolengine/axmol/issues)\n`);
  answer.push(`- 💬 [社区讨论](https://github.com/axmolengine/axmol/discussions)\n\n`);

  answer.push(`---\n`);
  answer.push(`*本回答基于 ${new Date().toLocaleString()} 的实时搜索结果生成*\n`);

  return answer.join("");
}

/**
 * 基于知识库生成答案
 */
function generateKnowledgeBasedAnswer(
  question: string,
  analysis: any,
  includeCode: boolean,
  language: string
): string {
  const categories = analysis.categories;

  if (categories.includes("sprite")) {
    return generateSpriteAnswer(question, language);
  } else if (categories.includes("animation")) {
    return generateAnimationAnswer(question, language);
  } else if (categories.includes("physics")) {
    return generatePhysicsAnswer(question, language);
  } else if (categories.includes("ui")) {
    return generateUIAnswer(question, language);
  } else if (categories.includes("audio")) {
    return generateAudioAnswer(question, language);
  } else if (categories.includes("scene")) {
    return generateSceneAnswer(question, language);
  } else if (categories.includes("3d")) {
    return generate3DAnswer(question, language);
  } else {
    return generateGeneralAnswer(question, language);
  }
}

/**
 * 基于文档生成答案
 */
function generateDocumentBasedAnswer(
  question: string,
  analysis: any,
  wikiResults: string,
  _includeCode: boolean,
  _language: string
): string {
  // 从 Wiki 结果中提取关键信息
  const answer = [];

  answer.push("根据 Axmol 官方文档，针对您的问题：\n\n");

  // 这里可以添加更复杂的文档解析逻辑
  if (wikiResults.includes("Sprite")) {
    answer.push("**精灵相关**:\n");
    answer.push("- 使用 `Sprite::create()` 创建精灵对象\n");
    answer.push("- 通过 `setPosition()` 设置位置\n");
    answer.push("- 使用 `addChild()` 添加到场景中\n\n");
  }

  if (wikiResults.includes("Animation")) {
    answer.push("**动画相关**:\n");
    answer.push("- 使用 `Animation::create()` 创建动画\n");
    answer.push("- 通过 `Animate::create()` 创建动画动作\n");
    answer.push("- 使用 `runAction()` 执行动画\n\n");
  }

  return answer.join("");
}

/**
 * 生成代码示例
 */
function generateCodeExample(
  question: string,
  analysis: any,
  language: string
): string {
  const categories = analysis.categories;

  if (categories.includes("sprite")) {
    return generateSpriteCodeExample(language);
  } else if (categories.includes("animation")) {
    return generateAnimationCodeExample(language);
  } else if (categories.includes("physics")) {
    return generatePhysicsCodeExample(language);
  } else if (categories.includes("3d")) {
    return generate3DCodeExample(language);
  } else {
    return generateBasicCodeExample(language);
  }
}

// 各种具体答案生成函数
function generateSpriteAnswer(question: string, _language: string): string {
  const isAboutBitmap = question.includes("点阵图") || question.includes("位图") || question.includes("bitmap") || question.includes("bmp");
  
  if (isAboutBitmap) {
    return `
Axmol 中加载点阵图/位图的方法：

1. **支持的格式**: PNG, JPG, BMP, TGA, PVR, KTX, ASTC 等
2. **加载位图**: 使用 \`Sprite::create()\` 加载各种格式图像
3. **纹理缓存**: \`TextureCache\` 管理纹理内存和加载
4. **像素操作**: \`Image\` 类提供像素级别的操作
5. **点阵字体**: \`BMFont\` 支持点阵字体文件(.fnt)
6. **内存管理**: 合理使用纹理压缩和释放

点阵图适合像素艺术风格游戏和需要精确像素控制的场景。
`;
  } else {
    return `
在 Axmol 中处理精灵的基本步骤：

1. **创建精灵**: 使用 \`Sprite::create()\` 方法
2. **设置属性**: 位置、缩放、旋转等
3. **添加到场景**: 使用 \`addChild()\` 方法
4. **管理生命周期**: 适当的内存管理

精灵是 Axmol 中最基础的可视化对象，支持纹理渲染、变换操作和动画播放。
`;
  }
}

function generateAnimationAnswer(_question: string, _language: string): string {
  return `
Axmol 动画系统包含几种类型：

1. **帧动画**: 使用 \`Animation\` 和 \`Animate\`
2. **动作动画**: 使用 \`Action\` 系列类
3. **补间动画**: 位置、缩放、旋转等属性变化
4. **骨骼动画**: 复杂角色动画

选择合适的动画类型取决于您的具体需求和性能要求。
`;
}

function generatePhysicsAnswer(_question: string, _language: string): string {
  return `
Axmol 物理系统基于 Box2D，提供：

1. **物理世界**: \`PhysicsWorld\` 管理物理模拟
2. **刚体**: \`PhysicsBody\` 表示物理对象
3. **形状**: \`PhysicsShape\` 定义碰撞边界
4. **碰撞检测**: 事件监听和回调处理

物理系统适合需要真实物理效果的游戏场景。
`;
}

function generateUIAnswer(_question: string, _language: string): string {
  return `
Axmol UI 系统提供丰富的界面组件：

1. **基础组件**: Button、Label、EditBox 等
2. **布局管理**: Layout 系统支持多种布局方式
3. **事件处理**: 触摸和点击事件响应
4. **样式定制**: 支持自定义外观和主题

UI 系统设计注重跨平台兼容性和易用性。
`;
}

function generateAudioAnswer(_question: string, _language: string): string {
  return `
Axmol 音频系统支持：

1. **背景音乐**: 长时间播放的音乐文件
2. **音效**: 短促的游戏音效
3. **流式播放**: 大文件的流式音频
4. **音频控制**: 音量、暂停、循环等控制

音频系统针对不同平台进行了优化。
`;
}

function generateSceneAnswer(_question: string, _language: string): string {
  return `
Axmol 场景管理包括：

1. **场景创建**: \`Scene::create()\` 或 \`Scene::createWithPhysics()\`
2. **场景切换**: \`Director::replaceScene()\` 等方法
3. **层级管理**: 使用 \`Layer\` 和 \`Node\` 组织内容
4. **生命周期**: onEnter、onExit 等回调方法

良好的场景设计是游戏架构的基础。
`;
}

function generate3DAnswer(_question: string, _language: string): string {
  return `
Axmol 3D 开发支持完整的三维图形功能：

1. **3D场景**: 使用 \`Scene::createWithPhysics()\` 创建支持3D的场景
2. **摄像机系统**: \`Camera\` 类提供透视投影和正交投影
3. **3D模型**: 支持加载 .obj、.c3b 等格式的3D模型
4. **光照系统**: \`DirectionLight\`、\`PointLight\`、\`SpotLight\` 等光源
5. **材质系统**: \`Material\` 和 \`Technique\` 控制渲染效果
6. **3D变换**: 位移、旋转、缩放等3D空间变换

Axmol的3D功能继承自Cocos2d-x，提供了完整的3D游戏开发能力。
`;
}

function generateGeneralAnswer(_question: string, _language: string): string {
  return `
根据您的问题，这里是一些通用的 Axmol 开发建议：

1. **查阅文档**: 优先参考官方 Wiki 和 API 文档
2. **示例代码**: 学习引擎提供的示例项目
3. **社区支持**: 参与 GitHub 讨论和问题反馈
4. **最佳实践**: 遵循引擎推荐的开发模式

Axmol 是一个功能强大的跨平台游戏引擎，适合各种类型的游戏开发。
`;
}

// 代码示例生成函数
function generateSpriteCodeExample(language: string): string {
  if (language === "lua") {
    return `
\`\`\`lua
-- 创建精灵
local sprite = cc.Sprite:create("player.png")
sprite:setPosition(cc.p(100, 100))
self:addChild(sprite)

-- 设置属性
sprite:setScale(2.0)
sprite:setRotation(45)
\`\`\`
`;
  } else {
    return `
\`\`\`cpp
// 基础精灵创建
auto sprite = Sprite::create("player.png");
sprite->setPosition(Vec2(100, 100));
this->addChild(sprite);

// 加载BMP位图
auto bmpSprite = Sprite::create("bitmap.bmp");
bmpSprite->setPosition(Vec2(200, 200));
this->addChild(bmpSprite);

// 点阵字体加载
auto bmfont = Label::createWithBMFont("fonts/bitmap_font.fnt", "Hello World");
bmfont->setPosition(Vec2(300, 300));
this->addChild(bmfont);

// 纹理缓存管理
auto textureCache = Director::getInstance()->getTextureCache();
auto texture = textureCache->addImage("large_bitmap.png");
auto spriteFromTexture = Sprite::createWithTexture(texture);
\`\`\`
`;
  }
}

function generateAnimationCodeExample(language: string): string {
  if (language === "lua") {
    return `
\`\`\`lua
-- 创建帧动画
local frames = {}
for i = 1, 4 do
    local frame = cc.SpriteFrame:create("anim_" .. i .. ".png", cc.rect(0, 0, 64, 64))
    table.insert(frames, frame)
end

local animation = cc.Animation:createWithSpriteFrames(frames, 0.2)
local animate = cc.Animate:create(animation)
sprite:runAction(cc.RepeatForever:create(animate))
\`\`\`
`;
  } else {
    return `
\`\`\`cpp
// 创建帧动画
Vector<SpriteFrame*> frames;
for (int i = 1; i <= 4; i++) {
    auto frame = SpriteFrame::create(StringUtils::format("anim_%d.png", i), 
                                   Rect(0, 0, 64, 64));
    frames.pushBack(frame);
}

auto animation = Animation::createWithSpriteFrames(frames, 0.2f);
auto animate = Animate::create(animation);
sprite->runAction(RepeatForever::create(animate));
\`\`\`
`;
  }
}

function generatePhysicsCodeExample(_language: string): string {
  return `
\`\`\`cpp
// 创建物理世界场景
auto scene = Scene::createWithPhysics();
scene->getPhysicsWorld()->setGravity(Vec2(0, -980));

// 创建物理刚体
auto physicsBody = PhysicsBody::createBox(Size(50, 50));
physicsBody->setDynamic(true);

auto sprite = Sprite::create("box.png");
sprite->setPhysicsBody(physicsBody);
sprite->setPosition(Vec2(100, 200));
scene->addChild(sprite);
\`\`\`
`;
}

function generate3DCodeExample(_language: string): string {
  return `
\`\`\`cpp
// 创建3D场景和摄像机
auto scene = Scene::create();
auto camera = Camera::createPerspective(60, visibleSize.width/visibleSize.height, 1, 1000);
camera->setPosition3D(Vec3(0, 0, 100));
scene->addChild(camera);

// 加载3D模型 
auto sprite3d = Sprite3D::create("model.c3b");
sprite3d->setPosition3D(Vec3(0, 0, 0));
sprite3d->setRotation3D(Vec3(0, 180, 0));
scene->addChild(sprite3d);

// 添加光照
auto light = DirectionLight::create(Vec3(0, -1, 0), Color3B(255, 255, 255));
scene->addChild(light);

// 启用3D渲染
auto director = Director::getInstance();
director->setDepthTest(true);
\`\`\`
`;
}

function generateBasicCodeExample(_language: string): string {
  return `
\`\`\`cpp
// Axmol 基础代码结构
#include "axmol.h"
USING_NS_AX;

bool HelloWorld::init() {
    if (!Scene::init()) {
        return false;
    }
    
    // 您的代码逻辑
    auto visibleSize = Director::getInstance()->getVisibleSize();
    auto origin = Director::getInstance()->getVisibleOrigin();
    
    return true;
}
\`\`\`
`;
}

/**
 * 生成相关建议
 */
function generateRelatedSuggestions(analysis: any): string {
  const suggestions = [];

  suggestions.push(
    "- 查看 [Axmol 官方 Wiki](https://github.com/axmolengine/axmol/wiki) 获取最新文档"
  );
  suggestions.push("- 参考引擎自带的示例项目学习最佳实践");
  suggestions.push(
    "- 在 [GitHub Issues](https://github.com/axmolengine/axmol/issues) 中搜索相关问题"
  );
  suggestions.push("- 考虑性能影响，选择合适的实现方案");

  if (analysis.categories.includes("physics")) {
    suggestions.push("- 物理系统会消耗较多性能，合理使用");
  }

  if (analysis.categories.includes("platform")) {
    suggestions.push("- 注意不同平台的特殊要求和限制");
  }

  return suggestions.join("\n");
}

// 启动服务器
main().catch(error => {
  console.error("服务器启动失败:", error);
  process.exit(1);
});
