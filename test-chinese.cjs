const { spawn } = require('child_process');

const testData = {
  "jsonrpc": "2.0",
  "id": 15,
  "method": "tools/call",
  "params": {
    "name": "axmol_smart_assistant",
    "arguments": {
      "question": "如何创建精灵？",
      "includeCode": true,
      "language": "cpp"
    }
  }
};

const child = spawn('node', ['dist/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  encoding: 'utf8'
});

child.stdin.write(JSON.stringify(testData));
child.stdin.end();

child.stdout.on('data', (data) => {
  console.log(data.toString());
});

child.stderr.on('data', (data) => {
  console.error(data.toString());
});

child.on('close', (code) => {
  console.log(`进程退出，代码: ${code}`);
});
