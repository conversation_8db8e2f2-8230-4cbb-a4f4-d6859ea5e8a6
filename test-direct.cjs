const { spawn } = require('child_process');
const fs = require('fs');

// 读取测试数据
const testData = JSON.parse(fs.readFileSync('test-final.json', 'utf8'));

console.log('发送测试数据:', JSON.stringify(testData, null, 2));

const child = spawn('node', ['dist/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

child.stdin.write(JSON.stringify(testData));
child.stdin.end();

let output = '';
child.stdout.on('data', (data) => {
  output += data.toString();
});

child.stderr.on('data', (data) => {
  console.error('错误:', data.toString());
});

// 设置超时
const timeout = setTimeout(() => {
  console.log('测试超时，终止进程');
  child.kill();
}, 10000);

child.on('close', (code) => {
  clearTimeout(timeout);
  console.log('输出结果:');
  console.log(output);
  console.log(`进程退出代码: ${code}`);
});
