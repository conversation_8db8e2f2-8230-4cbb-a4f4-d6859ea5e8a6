# Axmol MCP 智能助手

> 🚀 **专业的 Axmol 游戏引擎开发助手** - 基于 MCP (Model Context Protocol) 架构的智能问答系统

一个功能强大的 Axmol 游戏引擎开发助手，通过智能问题分析和实时资源搜索技术，为开发者提供精准的技术支持和完整的代码示例。本项目提供统一的智能助手工具，能够实时搜索 Axmol 官方 Wiki、源码、示例和文档，完全依赖最新的在线资源，无本地知识库。

## 🛠️ 工具概览

本项目提供 **单一统一的智能助手工具** `axmol_smart_assistant`，而非多个独立工具。这种设计简化了使用流程，提供更一致的用户体验。

### 📋 工具详情
- **工具名称**: `axmol_smart_assistant`
- **功能**: 全面的 Axmol 开发问题解答
- **支持领域**: 15个技术领域（精灵、动画、物理、UI、3D等）
- **代码语言**: C++ 和 Lua
- **搜索能力**: 实时 Wiki 搜索 + 本地知识库

## ✨ 核心特性

### 🧠 智能问题分析
- **多维度分析**: 自动识别问题类型（how-to、定义、故障排除等）
- **领域分类**: 精确分类到精灵、动画、物理、UI、3D等具体领域
- **关键词提取**: 智能提取技术术语和API关键词
- **优先级排序**: 根据问题重要性自动排序搜索结果

### 📚 实时资源搜索
- **官方Wiki搜索**: 实时搜索Axmol官方Wiki文档（需要网络连接）
- **源码搜索**: 直接搜索GitHub仓库中的最新源码文件
- **示例代码搜索**: 搜索官方测试和示例代码
- **API文档搜索**: 搜索头文件获取最新API定义
- **多数据源并行**: 同时搜索多个数据源提高准确性
- **智能降级**: 网络异常时提供基于问题分析的建议

### 💻 多语言代码示例
- **C++代码**: 完整的C++实现示例，包含现代C++特性
- **Lua脚本**: Lua语言绑定示例和脚本模板
- **分类示例**: 按功能模块提供专门的代码模板
- **最佳实践**: 包含性能优化、内存管理和错误处理建议

### 🎯 专业领域支持
- **精灵渲染**: Sprite创建、纹理管理、渲染优化
- **动画系统**: 帧动画、Action动画、骨骼动画
- **物理引擎**: Box2D集成、碰撞检测、刚体模拟  
- **UI界面**: 组件使用、布局管理、事件处理
- **音频系统**: 背景音乐、音效播放、流媒体
- **场景管理**: 场景切换、生命周期、内存管理
- **输入处理**: 触摸、键盘、鼠标事件
- **跨平台**: iOS、Android、Windows、macOS特性
- **性能优化**: 内存管理、渲染优化、构建配置

## 🛠️ 安装和配置

### 📦 环境要求
- **Node.js**: >= 18.0.0
- **npm**: 最新版本
- **系统**: Windows, macOS, Linux

### ⚙️ 快速安装

#### 1. 克隆项目并安装依赖
```bash
# 克隆项目（请替换为实际的仓库地址）
git clone <your-repository-url>
cd AxmolEngine-mcp
npm install
```

#### 2. 构建项目
```bash
npm run build
```

#### 3. 验证安装
```bash
# 检查工具列表
echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}}' | node dist/index.js

# 测试智能助手功能
echo '{"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "axmol_smart_assistant", "arguments": {"question": "如何创建精灵？", "includeCode": true, "language": "cpp"}}}' | node dist/index.js
```

> 💡 **提示**: 验证测试需要网络连接以访问 Axmol Wiki。如果网络不可用，系统会自动使用本地知识库提供答案。

### 🔧 配置 Cursor IDE

#### 1. 打开 Cursor 配置文件
- **Windows**: `%APPDATA%\Cursor\User\globalStorage\cursor.settings.json`
- **macOS**: `~/Library/Application Support/Cursor/User/globalStorage/cursor.settings.json`
- **Linux**: `~/.config/Cursor/User/globalStorage/cursor.settings.json`

#### 2. 添加 MCP 服务器配置
```json
{
  "mcpServers": {
    "axmol-mcp-server": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "/your/absolute/path/to/AxmolEngine-mcp",
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

> ⚠️ **重要**:
> - 请将 `cwd` 替换为您的实际项目绝对路径
> - 确保路径中包含已构建的 `dist/index.js` 文件
> - Windows 用户请使用正斜杠或双反斜杠作为路径分隔符

#### 3. 重启 Cursor
配置完成后重启 Cursor，在聊天界面中即可使用 Axmol 助手功能。

### 🔌 配置其他 MCP 客户端

对于其他支持 MCP 的客户端（如 Claude Desktop、其他 AI 工具等），请参考其文档进行配置。基本配置模式相同，都需要指定：
- **command**: `node`
- **args**: `["dist/index.js"]`
- **cwd**: 项目绝对路径
- **env**: 环境变量（可选）

## 💡 使用指南

### 🚀 基本用法

配置成功后，您可以在 Cursor 中直接询问 Axmol 相关问题：

#### 🎮 游戏开发问题示例
```
"如何创建一个会旋转的精灵？"
"Axmol 物理系统怎么检测碰撞？"
"如何实现角色的跳跃动画？"
"怎么处理多点触摸事件？"
"如何优化游戏的渲染性能？"
```

#### ⚙️ 智能处理流程
系统会自动执行以下步骤：

1. **🔍 智能分析**: 识别问题类型和技术领域（精灵、动画、物理、UI、3D等）
2. **📖 实时搜索**: 搜索 Axmol 官方 Wiki 和相关文档（需要网络连接）
3. **💻 代码生成**: 提供完整的 C++ 和 Lua 代码示例
4. **🎯 建议推荐**: 给出最佳实践和性能优化建议
5. **🔗 资源链接**: 提供相关文档和参考资料链接

> 📡 **网络依赖**: 本工具需要网络连接以访问 Axmol Wiki。网络不可用时会自动使用内置知识库。

### 🎛️ 高级用法

#### 使用参数自定义输出
```javascript
// 基础问答
{"question": "如何创建精灵？"}

// 包含上下文的问题
{
  "question": "精灵动画不流畅",
  "context": "在移动设备上运行，帧率较低",
  "includeCode": true,
  "language": "cpp"
}

// 仅获取Lua代码示例
{
  "question": "如何播放背景音乐？",
  "includeCode": true,
  "language": "lua"
}
```

### 📚 支持的技术领域

#### 🎨 图形渲染
- **精灵管理**: 创建、变换、纹理、批处理
- **动画系统**: 帧动画、骨骼动画、补间动画
- **特效系统**: 粒子效果、着色器、后处理
- **渲染优化**: 批处理、纹理管理、LOD

#### ⚙️ 游戏逻辑  
- **物理模拟**: Box2D集成、碰撞检测、约束
- **场景管理**: 场景切换、层级组织、内存管理
- **事件系统**: 输入处理、自定义事件、消息传递
- **AI与逻辑**: 状态机、寻路、游戏AI

#### 🖥️ 平台特性
- **跨平台开发**: iOS、Android、Windows、macOS、Linux
- **性能优化**: 内存管理、CPU优化、GPU优化
- **构建系统**: CMake配置、打包发布、调试技巧
- **第三方集成**: SDK集成、插件开发、扩展功能

## 🔧 开发者指南

### 👨‍💻 本地开发

```bash
# 🔥 热重载开发模式
npm run dev

# 🔍 代码质量检查
npm run lint

# ✨ 代码格式化
npm run format

# 🧪 运行测试（如有）
npm test

# 📦 构建生产版本
npm run build
```

### 🛠️ 项目结构分析

```
AxmolEngine-mcp/
├── src/
│   ├── index.ts              # 🎯 核心服务器入口（829行完整实现）
│   │   ├── main()           # MCP服务器初始化和工具注册
│   │   ├── handleSmartAssistant() # 智能助手主处理器
│   │   ├── analyzeQuestion() # 问题分析引擎（15个技术领域）
│   │   ├── searchAxmolWiki() # Wiki搜索引擎（并行搜索）
│   │   └── generateComprehensiveAnswer() # 综合答案生成器
│   └── services/            # 🔮 扩展服务模块（预留，当前为空）
├── dist/                    # 📦 TypeScript编译输出目录
│   ├── index.js            # 编译后的主入口文件
│   ├── index.d.ts          # TypeScript类型定义
│   └── *.map               # Source Map文件
├── .eslintrc.json          # 📋 ESLint代码规范配置
├── .prettierrc             # 🎨 Prettier代码格式化配置
├── tsconfig.json           # ⚙️ TypeScript编译配置
├── package.json            # 📋 项目依赖和脚本配置
└── mcp-config.json         # 🔌 MCP服务器本地配置示例
```

### 🚨 故障排除指南

#### ❌ MCP 服务器启动失败
**症状**: Cursor无法加载服务器
**解决方案**:
- ✅ 确认 Node.js 版本 >= 18.0.0
- ✅ 验证项目路径配置正确（绝对路径）
- ✅ 运行 `npm run build` 重新构建
- ✅ 检查 `dist/index.js` 文件是否存在

#### 🌐 网络连接问题
**症状**: Wiki搜索失败或超时
**解决方案**:
- ✅ 检查网络连接和代理设置
- ✅ 确认可以访问 `https://github.com/axmolengine/axmol/wiki`
- ✅ 检查防火墙设置是否阻止了 Node.js 网络访问
- ✅ 系统会自动降级到本地知识库（无需手动干预）

#### 📝 响应质量问题
**症状**: 答案不够准确或完整
**优化技巧**:
- ✅ 提供更详细的上下文信息
- ✅ 使用具体的技术术语
- ✅ 指定代码语言偏好（C++/Lua）
- ✅ 描述具体的使用场景

#### 🐛 常见错误代码
```bash
# 依赖安装失败
npm cache clean --force && npm install

# TypeScript编译错误
npm run build -- --verbose

# ESLint规则冲突
npm run lint -- --fix
```

## 🤝 贡献指南

### 🎯 技术架构

#### 核心技术栈
- **语言**: TypeScript 5.3+ (严格模式，ESM模块)
- **运行时**: Node.js 18+ (支持最新ES特性)
- **协议**: Model Context Protocol (MCP) 0.4+
- **HTTP客户端**: Axios 1.6+ (Wiki文档搜索)
- **HTML解析**: Cheerio 1.0+ (Wiki内容提取和解析)
- **代码质量**: ESLint + Prettier + TypeScript严格检查
- **构建工具**: TypeScript编译器 (tsc)

#### 系统架构图
```
用户问题 → 问题分析器 → 搜索引擎 → 答案生成器 → 结构化输出
    ↓         ↓          ↓         ↓          ↓
  自然语言  领域分类   实时Wiki   代码示例   Markdown格式
  输入      关键词提取  搜索/降级  最佳实践   相关建议
           (15个领域)  (5秒超时)  (C++/Lua)  (链接资源)
```

### 🔧 添加新功能

#### 1. 扩展问题分析器
在 `src/index.ts` 的 `analyzeQuestion()` 函数中添加新的技术领域：
```typescript
// 在 categories 对象中添加新的分类
const categories = {
  yourNewCategory: ["关键词1", "关键词2", "keyword1", "keyword2"],
  // ... 现有的15个分类（sprite, animation, physics等）
};
```

#### 2. 添加专门的答案生成器
```typescript
function generateYourCategoryAnswer(question: string, language: string): string {
  return `
您的专门领域答案内容...
1. **要点一**: 详细说明和技术细节
2. **要点二**: 代码示例和最佳实践
3. **要点三**: 性能考虑和注意事项
`;
}
```

#### 3. 集成到主流程
在 `generateKnowledgeBasedAnswer()` 函数中添加新的分支处理：
```typescript
// 在现有的 if-else 链中添加新分支
if (categories.includes("yourNewCategory")) {
  return generateYourCategoryAnswer(question, language);
} else if (categories.includes("sprite")) {
  // ... 现有逻辑
}
```

### 📝 代码规范

#### TypeScript 规范
- ✅ 启用严格模式类型检查
- ✅ 使用明确的类型注解
- ✅ 避免 `any` 类型，使用 `unknown`
- ✅ 函数参数添加类型定义
- ✅ 使用接口定义复杂对象

#### 命名规范
- **函数**: camelCase (`generateAnswer`)
- **常量**: SCREAMING_SNAKE_CASE (`WIKI_BASE_URL`)
- **接口**: PascalCase (`AnalysisResult`)
- **文件**: kebab-case (`wiki-search.ts`)

#### 代码组织
```typescript
// 1. 导入语句
import { Server } from "@modelcontextprotocol/sdk";

// 2. 类型定义
interface QuestionAnalysis {
  categories: string[];
  searchTerms: string[];
}

// 3. 常量定义
const TIMEOUT_MS = 5000;

// 4. 主要函数
function analyzeQuestion(): QuestionAnalysis { }

// 5. 辅助函数
function extractKeywords(): string[] { }

// 6. 启动代码
main().catch(console.error);
```

### 🔬 测试指南

#### 单元测试示例
```typescript
// 测试问题分析功能
test('问题分析 - 精灵相关问题', () => {
  const result = analyzeQuestion("如何创建精灵？", "");
  expect(result.categories).toContain("sprite");
  expect(result.questionType).toBe("how-to");
});
```

#### 集成测试
```bash
# 测试完整工具调用
node dist/index.js <<< '{"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"axmol_smart_assistant","arguments":{"question":"如何创建精灵？"}}}'
```

### 🚀 发布流程

#### 版本管理
```bash
# 1. 更新版本号
npm version patch|minor|major

# 2. 生成变更日志
git log --oneline --since="2024-01-01" > CHANGELOG.md

# 3. 构建发布版本
npm run build

# 4. 标签发布
git tag -a v2.1.0 -m "Release v2.1.0"
git push origin v2.1.0
```

## 📜 许可证

**MIT License** - 详见 [LICENSE](LICENSE) 文件

## 🆘 支持和反馈

### 📞 联系方式
- 🐛 **Bug报告**: 请在项目仓库中提交 Issues
- 💡 **功能建议**: 欢迎提交功能请求和改进建议
- 📚 **文档问题**: 如发现文档错误或不清楚的地方，请及时反馈
- ⭐ **使用体验**: 欢迎分享使用心得和最佳实践
- 🔗 **Axmol官方**: [Axmol GitHub](https://github.com/axmolengine/axmol) | [官方Wiki](https://github.com/axmolengine/axmol/wiki)

### 📈 项目状态
- ✅ **当前版本**: v1.0.0
- 🔄 **开发状态**: 活跃维护中
- 🌟 **功能完整度**: 核心功能完整，持续优化中
- 🛡️ **安全性**: 无已知漏洞，定期更新依赖

## 📊 更新日志

### 🎉 v1.0.0 (当前版本)
- ✨ **核心功能**: 统一智能助手工具 `axmol_smart_assistant`
- 🧠 **智能分析**: 15个技术领域的问题分析和分类
- 💻 **代码生成**: 完整的 C++ 和 Lua 代码示例生成
- 🔍 **实时搜索**: Axmol 官方 Wiki 文档实时搜索
- 🛡️ **智能降级**: 网络异常时自动切换到本地知识库
- 📝 **完整文档**: 详细的安装、配置和使用指南
- 🔧 **开发工具**: 完整的 TypeScript 开发环境和代码质量工具

### 🚀 未来计划
- 📚 **扩展知识库**: 增加更多 Axmol 技术领域支持
- 🔌 **服务模块**: 开发 services 目录下的扩展功能
- 🌐 **多语言**: 支持更多编程语言的代码示例
- 📊 **性能优化**: 提升搜索速度和答案质量

---

## 📝 项目总结

**Axmol MCP 智能助手** 是一个专业的游戏开发工具，通过智能问题分析和实时文档搜索技术，为 Axmol 开发者提供精准的技术支持。项目采用现代化的 TypeScript + MCP 架构，提供统一的智能助手工具，具备高度的可扩展性和稳定性。

### 🎯 核心价值
- **🚀 高效开发**: 快速获得基于最新资源的技术答案和完整代码示例
- **📚 实时搜索**: 直接搜索 Axmol 官方资源，包括 Wiki、源码、示例和文档
- **🧠 智能分析**: 自动理解问题意图，支持15个技术领域分类
- **🔧 开发友好**: 完整的 TypeScript 开发环境和详细文档支持
- **🌐 完全实时**: 无本地知识库，完全依赖最新的在线资源
- **🛡️ 智能降级**: 网络异常时提供基于问题分析的实用建议

**感谢您使用 Axmol MCP 智能助手！** 🎮✨

> 如果这个工具对您有帮助，欢迎给项目点个 ⭐ Star！ 